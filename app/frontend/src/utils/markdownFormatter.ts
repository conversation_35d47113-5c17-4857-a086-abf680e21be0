import { AnalysisResult, FormData } from '../types';

// Helper function to safely convert any value to a string for markdown
const safeStringify = (value: any): string => {
  if (value === null || value === undefined) {
    return 'No information provided';
  }

  if (typeof value === 'string') {
    return value;
  }

  if (typeof value === 'object') {
    try {
      // If it's an object, try to format it nicely
      if (Array.isArray(value)) {
        return value.map(item => `- ${safeStringify(item)}`).join('\n');
      } else {
        // Format object as key-value pairs
        return Object.entries(value)
          .map(([key, val]) => `**${key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}:** ${safeStringify(val)}`)
          .join('\n\n');
      }
    } catch (error) {
      return JSON.stringify(value, null, 2);
    }
  }

  return String(value);
};

export const formatAnalysisResultsAsMarkdown = (
  result: AnalysisResult,
  formData: FormData
): string => {
  const timestamp = new Date().toLocaleString();

  // Debug logging
  console.log('Formatting analysis results:', result);
  console.log('Form data:', formData);

  let markdown = `# AI Hedge Fund Analysis Report

**Generated:** ${timestamp}
**Analysis Period:** ${formData.startDate} to ${formData.endDate}
**Initial Capital:** $${formData.initialCash.toLocaleString()}
**Margin Requirement:** $${formData.marginRequirement.toLocaleString()}

---

## 📊 Portfolio Configuration

### Selected Stocks
${formData.selectedStocks.map(stock =>
  `- **${stock.ticker}** - ${stock.name}${stock.sector ? ` (${stock.sector})` : ''}`
).join('\n')}

### AI Analysts Consulted
${formData.selectedAnalysts.map(analyst => `- ${analyst.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}`).join('\n')}

### LLM Configuration
- **Provider:** ${formData.selectedProvider}
- **Model:** ${formData.selectedModel}

---

## 🎯 Trading Decisions

`;

  // Add trading decisions
  if (result.decisions && Object.keys(result.decisions).length > 0) {
    Object.entries(result.decisions).forEach(([ticker, decision]) => {
      const actionEmoji = {
        buy: '📈',
        sell: '📉',
        short: '🔻',
        cover: '🔺',
        hold: '⏸️'
      }[decision.action?.toLowerCase()] || '❓';

      markdown += `### ${actionEmoji} ${ticker}

**Action:** ${decision.action?.toUpperCase() || 'UNKNOWN'}
**Quantity:** ${decision.quantity?.toLocaleString() || 'N/A'} shares
**Confidence:** ${Math.round(decision.confidence || 0)}%

**Reasoning:**
> ${safeStringify(decision.reasoning)}

`;
    });
  } else {
    markdown += `*No trading decisions generated. This could be due to:*
- Analysis still in progress
- No clear trading signals identified
- Insufficient data for decision making

`;
  }

  markdown += `---

## 🧠 Analyst Signals

`;

  // Add analyst signals
  if (result.analystSignals && Object.keys(result.analystSignals).length > 0) {
    Object.entries(result.analystSignals).forEach(([analyst, signals]) => {
      const analystName = analyst.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
      markdown += `### ${analystName}

`;

      if (signals && typeof signals === 'object') {
        Object.entries(signals).forEach(([ticker, signal]) => {
          const signalEmoji = {
            bullish: '🟢',
            bearish: '🔴',
            neutral: '🟡'
          }[signal.signal?.toLowerCase()] || '⚪';

          markdown += `#### ${signalEmoji} ${ticker} - ${signal.signal?.toUpperCase() || 'UNKNOWN'}

**Confidence:** ${Math.round(signal.confidence || 0)}%

**Analysis:**
${safeStringify(signal.reasoning)}

`;
        });
      } else {
        markdown += `*No signals available for this analyst.*

`;
      }
    });
  } else {
    markdown += `*No analyst signals available. This could be due to:*
- Analysis still in progress
- Analysts haven't completed their evaluation
- Technical issues during analysis

`;
  }

  // Add Analysis Matrix before Summary
  markdown += `---

## 📊 Analysis Matrix

`;

  // Create matrix if we have analyst signals
  if (result.analystSignals && Object.keys(result.analystSignals).length > 0) {
    // Get all unique tickers from the analysis
    const allTickers = new Set<string>();
    Object.values(result.analystSignals).forEach(signals => {
      if (signals && typeof signals === 'object') {
        Object.keys(signals).forEach(ticker => allTickers.add(ticker));
      }
    });

    const tickerList = Array.from(allTickers).sort();
    const analysts = Object.keys(result.analystSignals);

    if (tickerList.length > 0 && analysts.length > 0) {
      // Create HTML table instead of markdown table for better compatibility
      markdown += '\n<table>\n';

      // Create table header
      markdown += '<thead>\n<tr>\n';
      markdown += '<th><strong>Analyst</strong></th>\n';
      tickerList.forEach(ticker => {
        markdown += `<th><strong>${ticker}</strong></th>\n`;
      });
      markdown += '</tr>\n</thead>\n';

      // Create table body
      markdown += '<tbody>\n';
      analysts.forEach(analyst => {
        const analystName = analyst.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
        markdown += '<tr>\n';
        markdown += `<td>${analystName}</td>\n`;

        tickerList.forEach(ticker => {
          const signals = result.analystSignals[analyst];
          if (signals && signals[ticker]) {
            const signal = signals[ticker];
            const signalEmoji = {
              bullish: '🟢',
              bearish: '🔴',
              neutral: '🟡'
            }[signal.signal?.toLowerCase()] || '⚪';

            const signalText = signal.signal?.toUpperCase() || 'UNKNOWN';
            const confidence = Math.round(signal.confidence || 0);
            markdown += `<td>${signalEmoji} ${signalText} (${confidence}%)</td>\n`;
          } else {
            markdown += '<td>—</td>\n';
          }
        });
        markdown += '</tr>\n';
      });
      markdown += '</tbody>\n';
      markdown += '</table>\n\n';
      markdown += `*This matrix shows each analyst's signal for each ticker with confidence percentage.*\n\n`;
    }
  } else {
    markdown += `*No analyst signals available for matrix display.*\n\n`;
  }

  markdown += `---

## 📈 Summary

This analysis was conducted using ${formData.selectedAnalysts.length} AI analyst${formData.selectedAnalysts.length !== 1 ? 's' : ''} across ${formData.selectedStocks.length} stock${formData.selectedStocks.length !== 1 ? 's' : ''}.

### Key Metrics
- **Stocks Analyzed:** ${formData.selectedStocks.length}
- **Analysts Consulted:** ${formData.selectedAnalysts.length}
- **Total Decisions:** ${result.decisions ? Object.keys(result.decisions).length : 0}
- **Analysis Model:** ${formData.selectedProvider} ${formData.selectedModel}

### Risk Disclaimer
> ⚠️ **Important:** This analysis is generated by AI and should not be considered as financial advice. Always conduct your own research and consult with qualified financial advisors before making investment decisions. Past performance does not guarantee future results.

---

*Report generated by AI Hedge Fund Analysis Platform*  
*Powered by ${formData.selectedProvider} ${formData.selectedModel}*
`;

  return markdown;
};

export const formatProgressAsMarkdown = (progress: any[]): string => {
  if (!progress || progress.length === 0) {
    return '# Analysis Progress\n\n*No progress updates available.*';
  }

  let markdown = `# Analysis Progress

**Last Updated:** ${new Date().toLocaleString()}

## Progress Log

`;

  progress.forEach((update, index) => {
    const timestamp = new Date(update.timestamp || Date.now()).toLocaleTimeString();
    markdown += `${index + 1}. **${timestamp}** - ${update.agentName}${update.ticker ? ` (${update.ticker})` : ''}: ${update.status}\n`;
  });

  return markdown;
};

export const generateSampleMarkdown = (): string => {
  return `# Sample AI Hedge Fund Analysis Report

**Generated:** ${new Date().toLocaleString()}  
**Analysis Period:** 2024-03-01 to 2024-06-01  
**Initial Capital:** $100,000  

---

## 📊 Portfolio Configuration

### Selected Stocks
- **AAPL** - Apple Inc. (Technology)
- **MSFT** - Microsoft Corporation (Technology)
- **GOOGL** - Alphabet Inc. (Technology)

### AI Analysts Consulted
- Warren Buffett
- Peter Lynch
- Technical Analyst

---

## 🎯 Trading Decisions

### 📈 AAPL

**Action:** BUY  
**Quantity:** 50 shares  
**Confidence:** 85%  

**Reasoning:**
> Strong fundamentals with consistent revenue growth and excellent brand loyalty. The company's ecosystem creates significant switching costs for customers.

### 📉 MSFT

**Action:** SELL  
**Quantity:** 25 shares  
**Confidence:** 72%  

**Reasoning:**
> While fundamentally strong, current valuation appears stretched relative to growth prospects. Taking profits after recent gains.

---

## 🧠 Analyst Signals

### Warren Buffett

#### 🟢 AAPL - BULLISH

**Confidence:** 88%

**Analysis:**
Apple demonstrates the characteristics I look for: a strong moat through brand loyalty and ecosystem lock-in, consistent profitability, and excellent management. The company generates substantial free cash flow and returns capital to shareholders effectively.

#### 🟡 MSFT - NEUTRAL

**Confidence:** 65%

**Analysis:**
Microsoft is a quality business with strong competitive advantages in enterprise software. However, at current valuations, the margin of safety is limited. Would be more interested at lower prices.

---

## 📊 Analysis Matrix

<table>
<thead>
<tr>
<th><strong>Analyst</strong></th>
<th><strong>AAPL</strong></th>
<th><strong>MSFT</strong></th>
<th><strong>GOOGL</strong></th>
</tr>
</thead>
<tbody>
<tr>
<td>Warren Buffett</td>
<td>🟢 BULLISH (88%)</td>
<td>🟡 NEUTRAL (65%)</td>
<td>🟢 BULLISH (82%)</td>
</tr>
<tr>
<td>Peter Lynch</td>
<td>🟢 BULLISH (91%)</td>
<td>🟢 BULLISH (78%)</td>
<td>🟡 NEUTRAL (68%)</td>
</tr>
<tr>
<td>Technical Analyst</td>
<td>🔴 BEARISH (73%)</td>
<td>🟢 BULLISH (85%)</td>
<td>🟢 BULLISH (79%)</td>
</tr>
</tbody>
</table>

*This matrix shows each analyst's signal for each ticker with confidence percentage.*

---

## 📈 Summary

This analysis was conducted using 3 AI analysts across 3 stocks.

### Key Metrics
- **Stocks Analyzed:** 3
- **Analysts Consulted:** 3
- **Total Decisions:** 2
- **Analysis Model:** OpenAI GPT-4

### Risk Disclaimer
> ⚠️ **Important:** This analysis is generated by AI and should not be considered as financial advice.

---

*Report generated by AI Hedge Fund Analysis Platform*
`;
};
